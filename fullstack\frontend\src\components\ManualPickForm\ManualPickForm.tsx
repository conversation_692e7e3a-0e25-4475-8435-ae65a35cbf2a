import React, { useState, useEffect } from "react";
import { ManualPick, ExpertConfidence } from "../../types/manualPick";
import { Expert } from "../../types/expert";
import { insertEvent } from "../../utils/api";
import { getExpertsByDate } from "../../api/expertPredictions";
import DateSelector from "../DateSelector/DateSelector";
import { toast } from "react-toastify";
import ToggleSwitch from "../ToggleSwitch/ToggleSwitch";
import { normalizeStatType } from "../../utils/dataTransforms";
import { invalidateEventsCache } from "../../utils/cache";

interface ManualPickFormProps {
  adminPassword: string;
}

const CUSTOM_SENTINEL = "__CUSTOM__";

// Default event date when none is pinned in localStorage
const DEFAULT_EVENT_DATE = "2025-05-28";

const ManualPickForm: React.FC<ManualPickFormProps> = ({ adminPassword }) => {
  // Local string states for inputs that accept decimal numbers to avoid premature parsing
  const [oddsInput, setOddsInput] = useState<string>("1.0");
  const [statThresholdInput, setStatThresholdInput] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>(DEFAULT_EVENT_DATE);
  const [dateFilteredExperts, setDateFilteredExperts] = useState<Expert[]>([]);
  const [expertsLoading, setExpertsLoading] = useState<boolean>(true);
  const [expertsError, setExpertsError] = useState<string | null>(null);
  const [isDatePinned, setIsDatePinned] = useState<boolean>(false);

  const [form, setForm] = useState<ManualPick>({
    name: "",
    odds: 1.0,
    league: [],
    pick_origin: [{ name: "", confidence: 50, team: "", stat_threshold: 0 }],
    reusable: true,
    capital_limit: 0,
    mutual_exclusion: -1,
    pick_type: "Prop",
    player_team: "",
    stat_type: "Points",
    prediction: 1,
    player_name: "",
    stat_threshold: undefined,
    eventDate: selectedDate,
    team_a: "",
    team_b: "",
  });

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [customInputs, setCustomInputs] = useState<Record<number, string>>({});
  const [customStatTypeInputs, setCustomStatTypeInputs] = useState<
    Record<number, string>
  >({});

  const availableLeagues: string[] = ["NBA", "NFL", "MLB", "NHL"];

  useEffect(() => {
    const pinnedDate = localStorage.getItem("pinnedEventDate");
    if (pinnedDate) {
      setSelectedDate(pinnedDate);
      setIsDatePinned(true);
    } else {
      // Ensure first-time users still start on default date
      setSelectedDate(DEFAULT_EVENT_DATE);
    }
  }, []);

  useEffect(() => {
    const fetchExpertsForDate = async () => {
      console.log(`🔄 Fetching experts for date: ${selectedDate}`);
      setExpertsLoading(true);
      setExpertsError(null);
      try {
        const experts = await getExpertsByDate(selectedDate);
        console.log(
          `✅ Fetched ${experts.length} experts for ${selectedDate}:`,
          experts
        );
        setDateFilteredExperts(experts);
      } catch (error) {
        console.error("❌ Failed to fetch experts for date:", error);
        setExpertsError(
          error instanceof Error ? error.message : "An unknown error occurred"
        );
      } finally {
        setExpertsLoading(false);
      }
    };

    fetchExpertsForDate();
  }, [selectedDate]);

  const handleInputChange = (
    field: keyof ManualPick,
    value: string | number | boolean | string[] | undefined
  ): void => {
    setForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleLeagueToggle = (league: string): void => {
    setForm((prev) => ({
      ...prev,
      league: prev.league.includes(league)
        ? prev.league.filter((l) => l !== league)
        : [...prev.league, league],
    }));
  };

  const handleExpertChange = (
    index: number,
    field: keyof ExpertConfidence,
    value: string | number
  ): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.map((expert, i) =>
        i === index ? { ...expert, [field]: value } : expert
      ),
    }));
  };

  const handleCustomInputChange = (index: number, value: string): void => {
    setCustomInputs((prev) => ({ ...prev, [index]: value }));
  };

  const addExpert = (): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: [
        ...prev.pick_origin,
        { name: "", confidence: 50, team: "", stat_threshold: 0 },
      ],
    }));
  };

  const removeExpert = (index: number): void => {
    setForm((prev) => ({
      ...prev,
      pick_origin: prev.pick_origin.filter((_, i) => i !== index),
    }));
    setCustomInputs((prev) => {
      const newInputs = { ...prev };
      delete newInputs[index];
      return newInputs;
    });
    setCustomStatTypeInputs((prev) => {
      const newInputs = { ...prev };
      delete newInputs[index];
      return newInputs;
    });
  };

  const handleDateLockToggle = (locked: boolean): void => {
    setIsDatePinned(locked);
    if (locked) {
      localStorage.setItem("pinnedEventDate", selectedDate);
      toast.info("Date locked!");
    } else {
      localStorage.removeItem("pinnedEventDate");
      toast.info("Date unlocked!");
    }
  };

  const handleSubmit = async (
    e: React.FormEvent<HTMLFormElement>
  ): Promise<void> => {
    e.preventDefault();

    const resolvedPickOrigin = form.pick_origin.map((expert, index) => ({
      ...expert,
      name:
        expert.name === CUSTOM_SENTINEL
          ? customInputs[index] || ""
          : expert.name,
      // Convert confidence from percentage (0-100) to decimal (0.0-1.0) for backend
      confidence: expert.confidence / 100,
    }));

    if (
      !form.name ||
      !form.team_a ||
      !form.team_b ||
      form.league.length === 0 ||
      resolvedPickOrigin.length === 0 ||
      resolvedPickOrigin.some((expert) => !expert.name.trim())
    ) {
      toast.error(
        "Please fill all required fields including team names and expert names"
      );
      return;
    }

    // No additional validation needed since player_name and stat_threshold are now optional

    setIsSubmitting(true);

    try {
      const submissionData: ManualPick = {
        ...form,
        pick_origin: resolvedPickOrigin,
        admin_password: adminPassword,
        eventDate: selectedDate,
        player_name: form.name, // Use Pick Name for player_name field
        pick_type:
          form.pick_type === CUSTOM_SENTINEL
            ? ((customInputs[-1] || "Custom") as "Prop" | "Custom")
            : form.pick_type,
        stat_type: normalizeStatType(
          form.stat_type === CUSTOM_SENTINEL
            ? customInputs[-2] || "custom"
            : form.stat_type
        ),
      };

      console.log("🚀 Submitting event data:", {
        ...submissionData,
        admin_password: "[HIDDEN]",
        pick_origin: submissionData.pick_origin.map((expert) => ({
          ...expert,
          confidence: `${expert.confidence} (converted from ${
            expert.confidence * 100
          }%)`,
        })),
      });
      const response = await insertEvent(submissionData);
      console.log("📥 Server response:", response);

      if (response.success) {
        toast.success(`Pick submitted successfully! ${response.message || ""}`);
        console.log("✅ Event submission successful:", response);

        // Invalidate events cache so fresh data is fetched on next request
        invalidateEventsCache();
        console.log("🔄 Events cache invalidated after successful submission");
        // -- No longer automatically resetting form on submit. Clearing is handled by Clear Fields button.
        /* const preservedState = {
            reusable: form.reusable,
            capital_limit: form.capital_limit,
            mutual_exclusion: form.mutual_exclusion,
            eventDate: selectedDate,
        };
        setForm({
          name: "",
          odds: 1.0,
          league: [],
          pick_origin: [],
          pick_type: "MoneyLine",
          player_team: "",
          stat_type: "MoneyLine",
          prediction: 1,
          player_name: "",
          stat_threshold: undefined,
          team_a: "",
          team_b: "",
          ...preservedState,
        });
        setCustomInputs({});
        // reset local input states
        setOddsInput("1.0");
        setStatThresholdInput(""); */
      } else {
        toast.error(`Error: ${response.message || "Submission failed"}`);
      }
    } catch (error) {
      toast.error(
        `Network error: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset all form fields and local inputs
  const clearFields = (): void => {
    setForm({
      name: "",
      odds: 1.0,
      league: [],
      pick_origin: [],
      reusable: true,
      capital_limit: 0,
      mutual_exclusion: -1,
      pick_type: "Prop",
      player_team: "",
      stat_type: "Points",
      prediction: 1,
      player_name: "",
      stat_threshold: undefined,
      eventDate: selectedDate,
      team_a: "",
      team_b: "",
    });
    setCustomInputs({});
    setCustomStatTypeInputs({});
    setOddsInput("1.0");
    setStatThresholdInput("");
  };

  const errorBanner = expertsError ? (
    <div className="text-center text-red-400 mb-4">
      Error loading experts: {expertsError}
    </div>
  ) : null;

  return (
    <div className="max-w-2xl mx-auto p-6 bg-gray-900 bg-opacity-80 border border-[#58C612] rounded-xl shadow-lg text-white">
      <h2 className="text-2xl font-bold mb-6 text-center text-[#58C612]">
        Manual Pick Form
      </h2>

      {errorBanner}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="flex-grow">
            <DateSelector
              label="Event Date *"
              value={selectedDate}
              onChange={setSelectedDate}
            />
          </div>
          <ToggleSwitch
            label="Lock Date"
            checked={isDatePinned}
            onChange={handleDateLockToggle}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Type
          </label>
          <select
            value={form.pick_type}
            onChange={(e) =>
              handleInputChange(
                "pick_type",
                e.target.value as "Prop" | "Custom" | "__CUSTOM__"
              )
            }
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none cursor-pointer"
          >
            <option value="Prop">Prop</option>
            <option value={CUSTOM_SENTINEL}>Custom</option>
          </select>

          {form.pick_type === CUSTOM_SENTINEL && (
            <input
              type="text"
              value={customInputs[-1] || ""}
              onChange={(e) =>
                setCustomInputs((prev) => ({ ...prev, [-1]: e.target.value }))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 mt-2 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
              placeholder="Enter custom pick type"
              required
            />
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Name *
          </label>
          <input
            type="text"
            value={form.name}
            onChange={(e) => handleInputChange("name", e.target.value)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., T. Haliburton"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Pick Team Name{" "}
            <span className="text-gray-500">
              (optional - for player stat picks)
            </span>
          </label>
          <input
            type="text"
            value={form.player_team}
            onChange={(e) => handleInputChange("player_team", e.target.value)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., Lakers"
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Team A *
            </label>
            <input
              type="text"
              value={form.team_a || ""}
              onChange={(e) => handleInputChange("team_a", e.target.value)}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="Over"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Team B *
            </label>
            <input
              type="text"
              value={form.team_b || ""}
              onChange={(e) => handleInputChange("team_b", e.target.value)}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="Under"
              required
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Leagues *
          </label>
          <div className="flex flex-wrap gap-2">
            {availableLeagues.map((league) => (
              <button
                key={league}
                type="button"
                onClick={() => handleLeagueToggle(league)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors cursor-pointer hover:cursor-pointer ${
                  form.league.includes(league)
                    ? "bg-[#58C612] text-black"
                    : "bg-[#233e6c] text-white hover:bg-[#1a2d54]"
                }`}
              >
                {league}
              </button>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Stat Threshold{" "}
            <span className="text-gray-500">
              (optional - for player stat picks)
            </span>
          </label>
          <input
            type="text"
            value={statThresholdInput}
            onChange={(e) => {
              const value = e.target.value;
              // Allow empty string, digits, decimal point, and decimal numbers
              // This allows typing intermediate states like "1.", ".", "1.5", etc.
              if (value === "" || /^(\d*\.?\d*|\.\d*)$/.test(value)) {
                setStatThresholdInput(value);
                const num = parseFloat(value);
                handleInputChange(
                  "stat_threshold",
                  isNaN(num) ? undefined : num
                );
              }
            }}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
            placeholder="e.g., 25.5"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Stat Type{" "}
            <span className="text-gray-500">
              (optional - for player stat picks)
            </span>
          </label>
          <select
            value={form.stat_type}
            onChange={(e) => handleInputChange("stat_type", e.target.value)}
            className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none cursor-pointer"
          >
            <option value="Points">Points</option>
            <option value="Assists">Assists</option>
            <option value="Rebounds">Rebounds</option>
            <option value="PRA">PRA</option>
            <option value="Blocks">Blocks</option>
            <option value="Steals">Steals</option>
            <option value="Turnovers">Turnovers</option>
            <option value="3-pt's">3-pt's</option>
            <option value={CUSTOM_SENTINEL}>Custom</option>
          </select>

          {form.stat_type === CUSTOM_SENTINEL && (
            <input
              type="text"
              value={customInputs[-2] || ""}
              onChange={(e) =>
                setCustomInputs((prev) => ({ ...prev, [-2]: e.target.value }))
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 mt-2 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
              placeholder="Enter custom stat type"
              required
            />
          )}
        </div>

        {(form.pick_type === "Prop" || form.pick_type === CUSTOM_SENTINEL) && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Prediction
            </label>
            <select
              value={form.prediction}
              onChange={(e) =>
                handleInputChange(
                  "prediction",
                  parseInt(e.target.value) as 1 | 0
                )
              }
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none cursor-pointer"
            >
              <option value={1}>Over/Higher</option>
              <option value={0}>Under/Lower</option>
            </select>
          </div>
        )}

        <div>
          <div className="flex justify-between items-center mb-4">
            <label className="block text-lg font-semibold text-gray-200">
              Expert Predictions <span className="text-red-500">*</span>
            </label>
            <button
              type="button"
              onClick={addExpert}
              className="px-4 py-2 bg-[#58C612] text-black font-semibold rounded-md hover:bg-[#449e10] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-[#58C612] transition-colors cursor-pointer"
            >
              Add Expert
            </button>
          </div>

          {expertsLoading ? (
            <div className="text-center text-gray-400">
              Loading experts for selected date...
            </div>
          ) : (
            form.pick_origin.map((expert, index) => (
              <div
                key={index}
                className="mb-4 p-4 bg-[#1a2d54] rounded-md border border-gray-600"
              >
                <div className="space-y-4">
                  {/* First Row - Expert Selection */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Expert Name
                      </label>
                      <select
                        value={expert.name}
                        onChange={(e) =>
                          handleExpertChange(index, "name", e.target.value)
                        }
                        className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white cursor-pointer"
                      >
                        <option value="">Select an Expert</option>
                        {dateFilteredExperts.map((exp) => (
                          <option key={exp.id} value={exp.name}>
                            {exp.name}
                          </option>
                        ))}
                        <option value={CUSTOM_SENTINEL}>
                          -- Add Custom --
                        </option>
                      </select>

                      {expert.name === CUSTOM_SENTINEL && (
                        <input
                          type="text"
                          value={customInputs[index] || ""}
                          onChange={(e) =>
                            handleCustomInputChange(index, e.target.value)
                          }
                          className="w-full bg-[#233e6c] rounded-md p-2.5 mt-2 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
                          placeholder="Enter custom expert name"
                          required
                        />
                      )}
                    </div>

                    <div className="flex items-end">
                      <button
                        type="button"
                        onClick={() => removeExpert(index)}
                        className="w-full px-3 py-2.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 transition-colors cursor-pointer"
                      >
                        Remove Expert
                      </button>
                    </div>
                  </div>

                  {/* Second Row - Details */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Confidence %
                      </label>
                      <div className="flex items-center gap-1">
                        <input
                          type="text"
                          value={
                            expert.confidence === 0 ? "" : expert.confidence
                          }
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === "" || /^(\d*\.?\d*)$/.test(value)) {
                              const num = parseFloat(value);
                              if (value === "") {
                                handleExpertChange(index, "confidence", 0);
                              } else if (!isNaN(num)) {
                                const bounded = Math.min(100, Math.max(0, num));
                                handleExpertChange(
                                  index,
                                  "confidence",
                                  bounded
                                );
                              }
                            }
                          }}
                          className="w-24 bg-[#233e6c] rounded-md p-2.5 text-center focus:ring-2 focus:ring-[#58C612] outline-none text-white"
                          placeholder="50"
                        />
                        <span className="text-gray-400 text-sm">%</span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Stat Threshold
                      </label>
                      <input
                        type="text"
                        className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
                        value={
                          expert.stat_threshold === 0
                            ? ""
                            : expert.stat_threshold
                        }
                        onChange={(e) => {
                          const value = e.target.value;
                          if (value === "" || /^(\d*\.?\d*)$/.test(value)) {
                            const num = parseFloat(value);
                            if (value === "") {
                              handleExpertChange(index, "stat_threshold", 0);
                            } else if (!isNaN(num)) {
                              handleExpertChange(index, "stat_threshold", num);
                            }
                          }
                        }}
                        placeholder="0.0"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Team
                      </label>
                      <input
                        type="text"
                        className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white"
                        value={expert.team || ""}
                        onChange={(e) =>
                          handleExpertChange(index, "team", e.target.value)
                        }
                        placeholder="e.g., Lakers"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Prediction Time
                      </label>
                      <input
                        type="time"
                        className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none text-white cursor-pointer [&::-webkit-calendar-picker-indicator]:filter [&::-webkit-calendar-picker-indicator]:invert [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                        value={expert.prediction_time || ""}
                        onChange={(e) =>
                          handleExpertChange(
                            index,
                            "prediction_time",
                            e.target.value
                          )
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        <div className="border-t border-gray-700 pt-6 space-y-4">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={form.reusable}
                onChange={(e) =>
                  handleInputChange("reusable", e.target.checked)
                }
                className="h-4 w-4 rounded border-gray-300 text-[#58C612] focus:ring-[#58C612]"
              />
              <span className="ml-2 text-gray-300">Reusable Pick</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Capital Limit <span className="text-gray-500">(optional)</span>
            </label>
            <input
              type="text"
              value={form.capital_limit}
              onChange={(e) => {
                const value = e.target.value;
                if (value === "" || /^\d+$/.test(value)) {
                  handleInputChange(
                    "capital_limit",
                    value === "" ? 0 : parseInt(value)
                  );
                }
              }}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="0 for no limit"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Mutual Exclusion Group{" "}
              <span className="text-gray-500">(optional)</span>
            </label>
            <input
              type="text"
              value={form.mutual_exclusion}
              onChange={(e) => {
                const value = e.target.value;
                if (value === "" || /^-?\d+$/.test(value)) {
                  handleInputChange(
                    "mutual_exclusion",
                    value === "" ? -1 : parseInt(value)
                  );
                }
              }}
              className="w-full bg-[#233e6c] rounded-md p-2.5 focus:ring-2 focus:ring-[#58C612] outline-none"
              placeholder="-1 for no exclusion"
            />
          </div>
        </div>

        <button
          type="submit"
          disabled={isSubmitting || expertsLoading}
          className="w-full py-3 px-4 bg-[#58C612] text-black font-bold rounded-md hover:bg-[#449e10] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-[#58C612] transition-colors disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
        >
          {isSubmitting ? "Submitting..." : "Submit Pick"}
        </button>

        <button
          type="button"
          onClick={clearFields}
          className="w-full mt-3 py-3 px-4 bg-blue-500 text-white font-bold rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-blue-500 transition-colors cursor-pointer"
        >
          Clear Fields
        </button>
      </form>
    </div>
  );
};

export default ManualPickForm;
